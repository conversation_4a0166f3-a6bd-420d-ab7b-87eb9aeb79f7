const express = require("express");
// Temporarily comment out controller import to debug path-to-regexp error
// const listingController = require("../controllers/listingController");
// Temporarily comment out validation import to debug path-to-regexp error
// const {
//   validateListingQuery,
//   validateObjectId,
// } = require("../middleware/validation");

const router = express.Router();

/**
 * @swagger
 * /api/listings:
 *   get:
 *     summary: Get all scraped listings
 *     tags: [Listings]
 *     responses:
 *       200:
 *         description: A list of scraped listings
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Listing'
 *       500:
 *         description: An error occurred while fetching listings
 */
// Temporarily use a simple route to debug path-to-regexp error
router.get("/listings", (req, res) => {
  res.json({ message: "Listings endpoint working" });
});

// Comment out the new route to see if it's causing the issue
// router.get("/listings/:id", listingController.getListingById);

module.exports = router;
