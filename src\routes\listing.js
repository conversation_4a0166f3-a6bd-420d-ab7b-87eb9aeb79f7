const express = require("express");
const listingController = require("../controllers/listingController");
// Temporarily disable validation to test if express-validator is causing the issue
// const {
//   validateListingQuery,
//   validateObjectId,
// } = require("../middleware/validation");

const router = express.Router();

/**
 * @swagger
 * /api/listings:
 *   get:
 *     summary: Get all scraped listings
 *     tags: [Listings]
 *     responses:
 *       200:
 *         description: A list of scraped listings
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Listing'
 *       500:
 *         description: An error occurred while fetching listings
 */
router.get("/listings", listingController.getListings);

// Get specific listing by ID
router.get("/listings/:id", listingController.getListingById);

module.exports = router;
