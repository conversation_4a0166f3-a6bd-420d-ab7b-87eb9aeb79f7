const express = require("express");
const listingController = require("../controllers/listingController");
const {
  validateListingQuery,
  validateObjectId,
} = require("../middleware/validation");

const router = express.Router();

/**
 * @swagger
 * /api/listings:
 *   get:
 *     summary: Get all scraped listings
 *     tags: [Listings]
 *     responses:
 *       200:
 *         description: A list of scraped listings
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Listing'
 *       500:
 *         description: An error occurred while fetching listings
 */
router.get("/listings", validateListingQuery, listingController.getListings);

// Get specific listing by ID
router.get("/listings/:id", validateObjectId, listingController.getListingById);

module.exports = router;
