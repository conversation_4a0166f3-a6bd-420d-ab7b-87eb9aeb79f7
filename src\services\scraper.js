
const puppeteer = require('puppeteer');
const cheerio = require('cheerio');
const Listing = require('../models/Listing');
const { sendAlerts } = require('./alertService');

const scrapePararius = async () => {
  const browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox'] });
  const page = await browser.newPage();
  await page.goto('https://www.pararius.nl/huurwoningen/nederland', { waitUntil: 'networkidle2', timeout: 60000 });

  const html = await page.content();
  const $ = cheerio.load(html);

  const listings = [];
  $('.search-list__item--listing').each((index, element) => {
    const title = $(element).find('.listing-search-item__title').text().trim();
    const price = $(element).find('.listing-search-item__price').text().trim();
    const location = $(element).find('.listing-search-item__location').text().trim();
    const url = 'https://www.pararius.nl' + $(element).find('a').attr('href');

    if (title && price && location && url) {
      listings.push({ title, price, location, url });
    }
  });

  await browser.close();
  console.log(`Found ${listings.length} listings from Pararius.`);

  for (const listingData of listings) {
    try {
      const newListing = new Listing(listingData);
      await newListing.save();
      console.log(`Saved listing: ${newListing.title}`);
      sendAlerts(newListing);
    } catch (error) {
      if (error.code === 11000) { // Duplicate key error
        console.log(`Skipping duplicate listing: ${listingData.title}`);
      } else {
        console.error(`Error saving listing ${listingData.title}:`, error);
      }
    }
  }
};

const scrapeFunda = async () => {
  // Launch browser with optimized settings for Funda
  const browser = await puppeteer.launch({ 
    headless: false, // Using non-headless mode makes detection harder
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-infobars',
      '--window-position=0,0',
      '--ignore-certificate-errors',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-dev-shm-usage',
      '--disable-extensions'
    ] 
  });
  
  const page = await browser.newPage();
  
  // Set a realistic viewport
  await page.setViewport({
    width: 1920,
    height: 1080,
    deviceScaleFactor: 1,
  });
  
  // Set a realistic user agent
  await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');

  // Configure browser to mimic human behavior
  await page.setExtraHTTPHeaders({
    'Accept-Language': 'nl-NL,nl;q=0.9,en-US;q=0.8,en;q=0.7',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'sec-ch-ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"'
  });

  // Enable JavaScript  
  await page.setJavaScriptEnabled(true);
  
  // Add cookies to make it look like a returning user
  await page.setCookie(
    {
      name: 'OptanonAlertBoxClosed',
      value: new Date().toISOString(),
      domain: '.funda.nl',
      httpOnly: false,
      secure: true
    },
    {
      name: 'OptanonConsent',
      value: 'isGpcEnabled=0&datestamp=' + new Date().toISOString() + '&version=6.26.0',
      domain: '.funda.nl',
      httpOnly: false,
      secure: true
    },
    {
      name: 'ajs_anonymous_id',
      value: '%22' + Math.random().toString(36).substring(2, 15) + '%22',
      domain: '.funda.nl',
      httpOnly: false,
      secure: true
    }
  );

  try {
    // Navigate to the page with a realistic timeout and networkidle setting
    console.log('Navigating to Funda rental listings page...');
    await page.goto('https://www.funda.nl/zoeken/huur/', { 
      waitUntil: 'networkidle0', 
      timeout: 90000 
    });

    // Add random delay to simulate human behavior (2-5 seconds)
    const randomDelay = Math.floor(Math.random() * 3000) + 2000;
    console.log(`Waiting for ${randomDelay}ms to simulate human behavior...`);
    await new Promise(r => setTimeout(r, randomDelay));
    
    // Scroll down to trigger lazy loading content
    console.log('Scrolling through page to load all content...');
    await autoScroll(page);
    
    // Add another small delay after scrolling
    await new Promise(r => setTimeout(r, 1000));
    
    // Save HTML for inspection
    console.log('Saving page HTML for inspection...');
    const html = await page.content();
    require('fs').writeFileSync('funda_page.html', html); 
    
    // Method 1: Extract listings from JSON-LD metadata
    console.log('Extracting listings from JSON-LD metadata...');
    const listings = [];
    
    // Find the JSON-LD script containing listing data
    const jsonLdMatches = html.match(/\<script type="application\/ld\+json" data-hid="result-list-metadata"\>(.+?)\<\/script\>/s);
    
    if (jsonLdMatches && jsonLdMatches.length > 1) {
      try {
        const jsonLdData = JSON.parse(jsonLdMatches[1]);
        
        if (jsonLdData && jsonLdData.itemListElement && Array.isArray(jsonLdData.itemListElement)) {
          console.log(`Found ${jsonLdData.itemListElement.length} listings in JSON-LD data`);
          
          // Process each listing URL to extract data
          for (const item of jsonLdData.itemListElement) {
            if (item.url && item.url.includes('/huur/')) { // Only process rental listings
              const urlParts = item.url.split('/');
              
              // Extract ID from the URL (last part)
              const id = urlParts[urlParts.length - 1];
              
              // Extract property type and city from URL structure (path between 'huur' and property ID)
              let propertyType = 'woning'; // Default
              let city = '';
              let streetName = '';
              
              // Parse URL parts more accurately
              const huurIndex = urlParts.indexOf('huur');
              if (huurIndex !== -1 && huurIndex + 1 < urlParts.length) {
                // Property type is the first segment after 'huur'
                if (['appartement', 'huis', 'parkeergelegenheid', 'kamer'].includes(urlParts[huurIndex + 1])) {
                  propertyType = urlParts[huurIndex + 1];
                }
                
                // City is the next segment
                if (huurIndex + 2 < urlParts.length) {
                  city = urlParts[huurIndex + 2].replace(/-/g, ' ');
                }
                
                // Street name is the next segment
                if (huurIndex + 3 < urlParts.length - 1) {
                  streetName = urlParts[huurIndex + 3].replace(/-/g, ' ');
                }
              }
              
              // Load HTML into cheerio to extract price from the page
              const $ = cheerio.load(html);
              let price = '';
              let title = '';
              
              // Find all anchor links that match this property URL
              $(`a[href*="${id}"]`).each((_, element) => {
                const parentElement = $(element).closest('li');
                
                // Price extraction
                const priceElement = parentElement.find('p[data-test-id="price"]');
                if (priceElement.length && !price) {
                  price = priceElement.text().trim();
                }
                
                // Title extraction
                const titleElement = parentElement.find('h2[data-test-id="street-name-house-number"]');
                if (titleElement.length && !title) {
                  title = titleElement.text().trim();
                }
                
                // Location extraction
                const locationElement = parentElement.find('p[data-test-id="postal-code-city"]');
                if (locationElement.length && !city) {
                  city = locationElement.text().trim();
                }
              });
              
              // Fallback to default formatting
              if (!title) {
                title = streetName || `${propertyType} in ${city}`;
              }
              
              // Default price if not found
              if (!price) {
                price = 'Prijs op aanvraag';
              }
              
              // Create a listing object with all available data
              const listingData = {
                title,
                url: item.url,
                location: city,
                propertyType,
                price,
                dateAdded: new Date()
              };
              
              // Add the listing to our collection
              console.log(`Found listing: ${listingData.title} - ${listingData.price} - ${listingData.location}`);
              listings.push(listingData);
            }
          }
        }
      } catch (jsonError) {
        console.error('Error parsing JSON-LD data:', jsonError);
      }
    }
    
    // Method 2: If JSON-LD doesn't work, try HTML parsing as fallback
    if (listings.length === 0) {
      console.log('Falling back to HTML parsing...');
      
      // Load into cheerio for parsing
      const $ = cheerio.load(html);
      
      // Look for listing cards - this is the most direct way
      $('ol[data-test-id="search-results"] > li').each((index, element) => {
        try {
          // Skip elements without data-test-id (those are usually ads)
          if (!$(element).attr('data-test-id')) return;

          // Extract key elements
          const titleElement = $(element).find('h2[data-test-id="street-name-house-number"]');
          const priceElement = $(element).find('p[data-test-id="price"]');
          const locationElement = $(element).find('p[data-test-id="postal-code-city"]');
          const linkElement = $(element).find('a[data-test-id="object-image-link"]');

          if (linkElement.length) {
            const url = 'https://www.funda.nl' + linkElement.attr('href');
            
            // Skip duplicates
            const isDuplicate = listings.some(listing => listing.url === url);
            if (isDuplicate) return;
            
            // Extract basic details
            const title = titleElement.length ? titleElement.text().trim() : '';
            const price = priceElement.length ? priceElement.text().trim() : 'Prijs op aanvraag';
            const location = locationElement.length ? locationElement.text().trim() : '';
            
            // Extract property type from URL
            const urlParts = url.split('/');
            const propertyIndex = urlParts.indexOf('huur') + 1;
            let propertyType = 'woning';
            if (propertyIndex < urlParts.length && ['appartement', 'huis', 'parkeergelegenheid', 'kamer'].includes(urlParts[propertyIndex])) {
              propertyType = urlParts[propertyIndex];
            }
            
            // Extract additional details if available
            let details = {};
            const detailsElement = $(element).find('ul[data-test-id="kenmerken-highlightedkeypoints"]');
            if (detailsElement.length) {
              detailsElement.find('li').each((i, detail) => {
                const detailText = $(detail).text().trim();
                if (detailText.includes('m²')) {
                  details.size = detailText;
                } else if (detailText.includes('slaapkamer')) {
                  details.bedrooms = detailText;
                }
              });
            }
            
            // Create listing object
            const listingData = {
              title: title || `${propertyType} in ${location}`,
              url,
              location,
              propertyType,
              price,
              ...details,
              dateAdded: new Date()
            };
            
            console.log(`Found listing via HTML: ${listingData.title} - ${listingData.price} - ${listingData.location}`);
            listings.push(listingData);
          }
        } catch (err) {
          console.error('Error processing listing element:', err);
        }
      });
      
      // If we still have no listings, try a more generic approach with all links
      if (listings.length === 0) {
        console.log('Trying generic link extraction approach...');
        $('a[href*="detail/huur"]').each((index, element) => {
          try {
            const url = $(element).attr('href');
            if (!url) return;
            
            const fullUrl = url.startsWith('http') ? url : 'https://www.funda.nl' + url;
            
            // Skip duplicates
            const isDuplicate = listings.some(listing => listing.url === fullUrl);
            if (isDuplicate) return;
            
            const urlParts = fullUrl.split('/');
            const huurIndex = urlParts.indexOf('huur');
            
            // Extract property type and city
            let propertyType = 'woning';
            let city = '';
            
            if (huurIndex !== -1) {
              if (huurIndex + 1 < urlParts.length && ['appartement', 'huis', 'parkeergelegenheid', 'kamer'].includes(urlParts[huurIndex + 1])) {
                propertyType = urlParts[huurIndex + 1];
              }
              
              if (huurIndex + 2 < urlParts.length) {
                city = urlParts[huurIndex + 2].replace(/-/g, ' ');
              }
            }
            
            // Try to find price nearby in the DOM
            let price = 'Prijs op aanvraag';
            const parentEl = $(element).closest('li, div, article');
            if (parentEl.length) {
              const priceText = parentEl.text();
              const priceMatch = priceText.match(/(€\s?[\d.,]+\s?[\w\s]*\/?\s?\w+|op\s+aanvraag)/i);
              if (priceMatch) {
                price = priceMatch[1].trim();
              }
            }
            
            listings.push({
              title: `${propertyType} in ${city}`,
              url: fullUrl,
              location: city,
              propertyType,
              price,
              dateAdded: new Date()
            });
            
          } catch (err) {
            console.error('Error processing listing link:', err);
          }
        });
      }
    }
    
    // Close the browser
    await browser.close();
    console.log(`Successfully extracted ${listings.length} listings from Funda`);
    
    if (listings.length > 0) {
      // Save listings to database and send alerts
      for (const listingData of listings) {
        console.log(`Funda: Processing listing: ${listingData.title} at ${listingData.url}`);
        try {
          const newListing = new Listing(listingData);
          await newListing.save();
          console.log(`Funda: Saved listing: ${newListing.title}`);
          sendAlerts(newListing);
        } catch (error) {
          if (error.code === 11000) { // Duplicate key error
            console.log(`Funda: Skipping duplicate listing: ${listingData.title}`);
          } else {
            console.error(`Funda: Error saving listing ${listingData.title}:`, error);
          }
        }
      }
    } else {
      console.log('No listings found on Funda');
    }
    
    return listings;
  } catch (error) {
    console.error('Error during Funda scraping:', error);
    await browser.close();
    return [];
  }
};

// Helper function to scroll the page to load all content
async function autoScroll(page) {
  await page.evaluate(async () => {
    await new Promise((resolve) => {
      let totalHeight = 0;
      const distance = 100;
      const timer = setInterval(() => {
        const scrollHeight = document.body.scrollHeight;
        window.scrollBy(0, distance);
        totalHeight += distance;

        // Add some random delay to make it look more human-like
        const randomDelay = Math.floor(Math.random() * 40) + 10;
        setTimeout(() => {}, randomDelay);

        if (totalHeight >= scrollHeight - window.innerHeight) {
          clearInterval(timer);
          resolve();
        }
      }, 100);
    });
  });
}

module.exports = { scrapePararius, scrapeFunda };


