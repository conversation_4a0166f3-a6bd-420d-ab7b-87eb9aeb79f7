
const express = require('express');
const mongoose = require('mongoose');
const schedule = require('node-schedule');
const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const config = require('./config/config');
const authRoutes = require('./routes/auth');
const scraperRoutes = require('./routes/scraper');
const listingRoutes = require('./routes/listing');
const { scrapePararius, scrapeFunda } = require('./services/scraper');

const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

mongoose.connect(config.mongoURI)
  .then(() => console.log('MongoDB connected'))
  .catch(err => console.log(err));

// Swagger definition
const swaggerOptions = {
  swaggerDefinition: {
    openapi: '3.0.0',
    info: {
      title: 'Zakmakelaar AI API Documentation',
      version: '1.0.0',
      description: 'API documentation for the Zakmakelaar AI backend application',
    },
    servers: [
      {
        url: `http://localhost:${port}`,
        description: 'Development server',
      },
    ],
    components: {
      schemas: {
        Listing: {
          type: 'object',
          properties: {
            title: { type: 'string' },
            price: { type: 'number' },
            location: { type: 'string' },
            url: { type: 'string' },
            size: { type: 'string' },
            description: { type: 'string' },
            timestamp: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  },
  apis: ['./src/routes/*.js'], // Path to the API docs
};

const swaggerDocs = swaggerJsdoc(swaggerOptions);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));

app.use('/api/auth', authRoutes);
app.use('/api', scraperRoutes);
app.use('/api', listingRoutes);

app.get('/', (req, res) => {
  res.send('Hello, zakmakelaar AI!');
});

// Schedule the scraper to run every 5 minutes
schedule.scheduleJob('*/5 * * * *', async () => {
  console.log('Running scraper...');
  //await scrapePararius();
  await scrapeFunda();
});

app.listen(port, () => {
  console.log(`Server is running on port ${port}`);
});
