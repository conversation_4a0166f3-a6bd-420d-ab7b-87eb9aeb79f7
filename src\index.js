const express = require("express");
const mongoose = require("mongoose");
const schedule = require("node-schedule");
const swaggerJsdoc = require("swagger-jsdoc");
const swaggerUi = require("swagger-ui-express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");

const config = require("./config/config");
const { connectDB } = require("./config/database");
const authRoutes = require("./routes/auth");
const scraperRoutes = require("./routes/scraper");
const listingRoutes = require("./routes/listing");
const { scrapePararius, scrapeFunda } = require("./services/scraper");

// Import middleware
const { globalErrorHandler, AppError } = require("./middleware/errorHandler");
// const { generalLimiter } = require("./middleware/rateLimiter"); // Keep this disabled for now

const app = express();
const port = config.port;

// Security middleware
app.use(helmet()); // Set security headers
app.use(
  cors({
    origin: config.corsOrigin,
    credentials: true,
  })
);

// Rate limiting - temporarily disabled for debugging
// app.use(generalLimiter);

// Logging middleware
if (config.nodeEnv === "development") {
  app.use(morgan("dev"));
} else {
  app.use(morgan("combined"));
}

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Connect to database
connectDB();

// Swagger definition
const swaggerOptions = {
  swaggerDefinition: {
    openapi: "3.0.0",
    info: {
      title: "Zakmakelaar AI API Documentation",
      version: "1.0.0",
      description:
        "API documentation for the Zakmakelaar AI backend application",
    },
    servers: [
      {
        url: `http://localhost:${port}`,
        description: "Development server",
      },
    ],
    components: {
      schemas: {
        Listing: {
          type: "object",
          properties: {
            title: { type: "string" },
            price: { type: "number" },
            location: { type: "string" },
            url: { type: "string" },
            size: { type: "string" },
            description: { type: "string" },
            timestamp: { type: "string", format: "date-time" },
          },
        },
      },
    },
  },
  apis: ["./src/routes/*.js"], // Path to the API docs
};

// Temporarily disable Swagger to debug path-to-regexp error
// const swaggerDocs = swaggerJsdoc(swaggerOptions);
// app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerDocs));

// Re-enabling routes gradually to identify the problematic one
// app.use("/api/auth", authRoutes);
// app.use("/api", scraperRoutes);
// app.use("/api", listingRoutes); // Temporarily disable to test

// Health check endpoint
app.get("/", (req, res) => {
  res.json({
    status: "success",
    message: "ZakMakelaar API is running!",
    version: "1.0.0",
    environment: config.nodeEnv,
    timestamp: new Date().toISOString(),
  });
});

// Health check endpoint for monitoring
app.get("/health", (req, res) => {
  res.json({
    status: "healthy",
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    database:
      mongoose.connection.readyState === 1 ? "connected" : "disconnected",
  });
});

// Handle undefined routes
app.all("*", (req, res, next) => {
  next(new AppError(`Can't find ${req.originalUrl} on this server!`, 404));
});

// Global error handling middleware (must be last)
app.use(globalErrorHandler);

// Schedule the scraper to run at configured intervals
const scrapingInterval = `*/${config.scrapingIntervalMinutes} * * * *`;
schedule.scheduleJob(scrapingInterval, async () => {
  console.log(
    `Running scraper... (every ${config.scrapingIntervalMinutes} minutes)`
  );
  try {
    //await scrapePararius();
    await scrapeFunda();
  } catch (error) {
    console.error("Scheduled scraping error:", error);
  }
});

// Graceful shutdown handling
process.on("SIGTERM", () => {
  console.log("👋 SIGTERM RECEIVED. Shutting down gracefully");
  server.close(() => {
    console.log("💥 Process terminated!");
  });
});

process.on("unhandledRejection", (err) => {
  console.log("UNHANDLED REJECTION! 💥 Shutting down...");
  console.log(err.name, err.message);
  server.close(() => {
    process.exit(1);
  });
});

const server = app.listen(port, () => {
  console.log(`🚀 Server is running on port ${port}`);
  console.log(`📚 API Documentation: http://localhost:${port}/api-docs`);
  console.log(`🏥 Health Check: http://localhost:${port}/health`);
  console.log(`🌍 Environment: ${config.nodeEnv}`);
});
