const mongoose = require('mongoose');
const config = require('./config/config');
const { scrapeFunda } = require('./services/scraper');

// Main function to run scraper with database connection
async function runScraper() {
  try {
    // Connect to MongoDB first
    console.log('Connecting to MongoDB...');
    await mongoose.connect(config.mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected successfully');

    // Run the scraper
    console.log('Starting Funda scraper...');
    const listings = await scrapeFunda();
    
    // Log results
    console.log(`Successfully extracted and processed ${listings.length} listings`);
    
    // Display a sample of the listings
    if (listings.length > 0) {
      console.log('Sample listings:');
      listings.slice(0, 3).forEach((listing, i) => {
        console.log(`\n--- Listing ${i+1} ---`);
        console.log(`Title: ${listing.title}`);
        console.log(`Price: ${listing.price}`);
        console.log(`Location: ${listing.location}`);
        console.log(`URL: ${listing.url}`);
        console.log(`Property Type: ${listing.propertyType}`);
        if (listing.size) console.log(`Size: ${listing.size}`);
        if (listing.bedrooms) console.log(`Bedrooms: ${listing.bedrooms}`);
      });
    }
  } catch (error) {
    console.error('Error running scraper:', error);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
    process.exit(0);
  }
}

// Run the scraper
runScraper();
