{"name": "zakmakelaar-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@sendgrid/mail": "^8.1.5", "bcrypt": "^6.0.0", "cheerio": "^1.1.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "node-schedule": "^2.1.1", "puppeteer": "^24.11.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^5.7.1"}, "devDependencies": {"nodemon": "^3.1.10"}}