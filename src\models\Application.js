
const mongoose = require('mongoose');

const applicationSchema = new mongoose.Schema({
  listing: { type: mongoose.Schema.Types.ObjectId, ref: 'Listing', required: true },
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  status: { type: String, enum: ['Applied', 'Viewing Scheduled', 'Rejected'], default: 'Applied' },
  date: { type: Date, default: Date.now },
});

module.exports = mongoose.model('Application', applicationSchema);
