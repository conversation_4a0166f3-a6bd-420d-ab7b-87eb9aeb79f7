
const mongoose = require('mongoose');

const listingSchema = new mongoose.Schema({
  title: { type: String, required: true },
  price: { type: String, required: true },
  location: { type: String, required: true },
  url: { type: String, required: true, unique: true },
  size: { type: String },
  bedrooms: { type: String },
  propertyType: { type: String },
  description: { type: String },
  dateAdded: { type: Date, default: Date.now },
  timestamp: { type: Date, default: Date.now },
});

module.exports = mongoose.model('Listing', listingSchema);
