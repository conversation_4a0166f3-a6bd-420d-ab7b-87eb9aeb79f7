
const express = require('express');
const scraperController = require('../controllers/scraperController');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Scraper
 *   description: Web scraping operations
 */

/**
 * @swagger
 * /api/scrape:
 *   post:
 *     summary: Manually trigger the web scraper
 *     tags: [Scraper]
 *     responses:
 *       200:
 *         description: Scraping completed successfully
 *       500:
 *         description: An error occurred while scraping
 */
router.post('/scrape', (req, res, next) => {
  console.log('[/api/scrape] route hit. Request body:', req.body);
  next();
}, (req, res, next) => scraperController.scrape(req, res, next));

module.exports = router;
