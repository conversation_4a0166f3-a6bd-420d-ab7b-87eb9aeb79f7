const User = require('../models/User');
const Listing = require('../models/Listing');
const sgMail = require('@sendgrid/mail');
const twilio = require('twilio');
const config = require('../config/config');

sgMail.setApiKey(config.sendgridApiKey);
const twilioClient = twilio(config.twilioAccountSid, config.twilioAuthToken);

const sendAlerts = async (listing) => {
  const users = await User.find();

  for (const user of users) {
    if (matchesPreferences(listing, user.preferences)) {
      sendEmail(user.email, listing);
      sendWhatsApp(user.profile.phone, listing);
    }
  }
};

const matchesPreferences = (listing, preferences) => {
  if (!preferences) return false;

  const { location, budget, rooms } = preferences;

  if (location && !listing.location.toLowerCase().includes(location.toLowerCase())) {
    return false;
  }

  if (budget && listing.price > budget) {
    return false;
  }

  if (rooms && listing.size && !listing.size.includes(rooms)) {
    return false;
  }

  return true;
};

const sendEmail = (to, listing) => {
  const msg = {
    to,
    from: '<EMAIL>', // Replace with your sender email
    subject: `New Listing: ${listing.title}`,
    html: `<p>A new listing matching your preferences is available:</p>
           <p><strong>Title:</strong> ${listing.title}</p>
           <p><strong>Price:</strong> ${listing.price}</p>
           <p><strong>Location:</strong> ${listing.location}</p>
           <p><a href="${listing.url}">View Listing</a></p>`,
  };

  sgMail.send(msg).catch(error => console.error(error));
};

const sendWhatsApp = (to, listing) => {
  if (!to) return;

  const message = `New Listing: ${listing.title}\nPrice: ${listing.price}\nLocation: ${listing.location}\n${listing.url}`;

  twilioClient.messages.create({
    body: message,
    from: 'whatsapp:+14155238886', // Twilio sandbox number
    to: `whatsapp:${to}`,
  }).catch(error => console.error(error));
};

module.exports = { sendAlerts };
