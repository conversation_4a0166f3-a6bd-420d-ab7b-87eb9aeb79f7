const User = require("../models/User");
const Listing = require("../models/Listing");
const config = require("../config/config");

// Lazy initialization of external services to avoid errors when credentials are not configured
let sgMail = null;
let twilioClient = null;

// Initialize SendGrid only if API key is provided
const initSendGrid = () => {
  if (!sgMail && config.sendgridApiKey) {
    sgMail = require("@sendgrid/mail");
    sgMail.setApiKey(config.sendgridApiKey);
  }
  return sgMail;
};

// Initialize Twilio only if credentials are provided
const initTwilio = () => {
  if (!twilioClient && config.twilioAccountSid && config.twilioAuthToken) {
    const twilio = require("twilio");
    twilioClient = twilio(config.twilioAccountSid, config.twilioAuthToken);
  }
  return twilioClient;
};

const sendAlerts = async (listing) => {
  const users = await User.find();

  for (const user of users) {
    if (matchesPreferences(listing, user.preferences)) {
      sendEmail(user.email, listing);
      sendWhatsApp(user.profile.phone, listing);
    }
  }
};

const matchesPreferences = (listing, preferences) => {
  if (!preferences) return false;

  const { location, budget, rooms } = preferences;

  if (
    location &&
    !listing.location.toLowerCase().includes(location.toLowerCase())
  ) {
    return false;
  }

  if (budget && listing.price > budget) {
    return false;
  }

  if (rooms && listing.size && !listing.size.includes(rooms)) {
    return false;
  }

  return true;
};

const sendEmail = (to, listing) => {
  const sgMailClient = initSendGrid();

  if (!sgMailClient) {
    console.log("SendGrid not configured, skipping email notification");
    return;
  }

  const msg = {
    to,
    from: config.sendgridFromEmail,
    subject: `New Listing: ${listing.title}`,
    html: `<p>A new listing matching your preferences is available:</p>
           <p><strong>Title:</strong> ${listing.title}</p>
           <p><strong>Price:</strong> ${listing.price}</p>
           <p><strong>Location:</strong> ${listing.location}</p>
           <p><a href="${listing.url}">View Listing</a></p>`,
  };

  sgMailClient
    .send(msg)
    .catch((error) => console.error("SendGrid error:", error));
};

const sendWhatsApp = (to, listing) => {
  if (!to) return;

  const twilioClientInstance = initTwilio();

  if (!twilioClientInstance) {
    console.log("Twilio not configured, skipping WhatsApp notification");
    return;
  }

  const message = `New Listing: ${listing.title}\nPrice: ${listing.price}\nLocation: ${listing.location}\n${listing.url}`;

  twilioClientInstance.messages
    .create({
      body: message,
      from: config.twilioWhatsAppFrom,
      to: `whatsapp:${to}`,
    })
    .catch((error) => console.error("Twilio error:", error));
};

module.exports = { sendAlerts };
